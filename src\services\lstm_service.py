#!/usr/bin/env python3
"""
Intel-Optimized LSTM Stock Prediction Service
6-feature input (adj close, volume, RSI-14, <PERSON><PERSON><PERSON> %) with 75-day window / 5-day horizon
Trains up to day -5, predicts 6 days (-4, -3, -2, -1, 0, +1) with bias offset only

CHANGELOG:
- Added temporal data leakage prevention with cutoff_offset=5 trading days
- Training data now filtered to Date <= target_date - 5 business days before label creation
- Added comprehensive logging for train start/end, rows kept/dropped, and cut-off dates
- Preserved existing shift(-5) logic within the temporally safe training slice
"""

import os
import sys
import json
from datetime import date, timedelta
from pathlib import Path

# Module-level constants - June 5, 2025 as day 0 (reference date)
REFERENCE_DATE = date(2025, 6, 5)  # Day 0 - consistent with other Python services

# Intel optimization environment variables
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
os.environ['TF_ENABLE_BF16_CONVOLUTIONS'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Enable oneDNN verbose if debug flag is set
if os.environ.get('LSTM_DEBUG_ONEDNN') == '1':
    os.environ['ONEDNN_VERBOSE'] = '1'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# Import required libraries
import pandas as pd
import numpy as np
from pandas.tseries.offsets import BDay
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import joblib
import tensorflow as tf
from tensorflow.keras import Sequential
from tensorflow.keras.layers import Dense, LSTM, Dropout, Input
from tensorflow.keras.callbacks import EarlyStopping

# Common Constants for Technical Indicator-based LSTM Prediction
WINDOW_DAYS = 75        # LSTM input sequence length (75 trading days)
HORIZON_DAYS = 5        # Prediction horizon (5 business days ahead)
RSI_PERIOD = 14         # RSI calculation period
BB_PERIOD = 20          # Bollinger Bands period
BB_STD_MULT = 2         # Bollinger Bands standard deviation multiplier

# Intel optimizations enabled via environment variables

# Configure TensorFlow threading for Intel Core Ultra 7 155H
tf.config.threading.set_intra_op_parallelism_threads(16)
tf.config.threading.set_inter_op_parallelism_threads(2)
tf.get_logger().setLevel('ERROR')

# Print startup log
print(f"✅  TensorFlow-Intel {tf.__version__} — oneDNN enabled", file=sys.stderr)


def apply_temporal_cutoff(df, target_date, cutoff_offset=5):
    """
    Apply temporal cut-off to prevent label leakage.

    Rule: Training data must end on target_date - cutoff_offset (trading days)
    to ensure that when we create labels with shift(-5), no future information
    beyond target_date - 1 is used.

    Parameters:
    -----------
    df : pandas.DataFrame
        Raw dataframe with Date column
    target_date : str or pd.Timestamp
        Target prediction date (day D)
    cutoff_offset : int
        Number of trading days to subtract (default=5)

    Returns:
    --------
    tuple: (filtered_df, cutoff_date, rows_kept, rows_dropped)
    """
    target_ts = pd.Timestamp(target_date)

    # Calculate cut-off date: target_date - cutoff_offset business days
    cutoff_date = target_ts - BDay(cutoff_offset)

    # Filter dataframe to dates <= cutoff_date
    original_rows = len(df)
    filtered_df = df[df['Date'] <= cutoff_date].copy()
    rows_kept = len(filtered_df)
    rows_dropped = original_rows - rows_kept

    return filtered_df, cutoff_date, rows_kept, rows_dropped


def compute_indicators(df):
    """
    Compute technical indicators for LSTM features

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with columns ['Date', 'close', 'volume'] (or similar)

    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    """
    # Ensure we have the required columns
    if 'Adj Close' in df.columns:
        df = df.rename(columns={'Adj Close': 'close'})
    if 'Volume' in df.columns:
        df = df.rename(columns={'Volume': 'volume'})

    # Sort by date to ensure proper calculation
    df = df.sort_values('Date').copy()

    # Calculate RSI(14)
    delta = df['close'].diff()
    gain = delta.clip(lower=0)
    loss = -delta.clip(upper=0)

    # Use exponential moving average for RSI calculation
    avg_gain = gain.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    avg_loss = loss.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    # Normalize RSI from 0-100 range to 0-1 scale
    df['rsi14'] = rsi / 100.0

    # Calculate Bollinger Bands
    sma = df['close'].rolling(window=BB_PERIOD).mean()
    std = df['close'].rolling(window=BB_PERIOD).std()
    bb_upper = sma + (BB_STD_MULT * std)
    bb_lower = sma - (BB_STD_MULT * std)

    # Calculate Bollinger Band features as percentages
    # bb_upper_pct: How far current price is above upper band (as percentage)
    df['bb_upper_pct'] = ((df['close'] - bb_upper) / bb_upper * 100).clip(lower=-100, upper=100)

    # bb_lower_pct: How far current price is above lower band (as percentage)
    df['bb_lower_pct'] = ((df['close'] - bb_lower) / bb_lower * 100).clip(lower=-100, upper=100)

    # bb_width_pct: Band width as percentage of middle line
    df['bb_width_pct'] = ((bb_upper - bb_lower) / sma * 100).clip(lower=0, upper=100)

    # Remove NaN values and reset index to ensure proper alignment
    df_clean = df.dropna().reset_index(drop=True)

    # Debug logging for technical indicator statistics
    print(f"Technical indicator stats:", file=sys.stderr)
    print(f"  Original data rows: {len(df)}, After NaN removal: {len(df_clean)}", file=sys.stderr)
    print(f"  RSI14 range: {df_clean['rsi14'].min():.3f} - {df_clean['rsi14'].max():.3f}", file=sys.stderr)
    print(f"  BB Upper %: {df_clean['bb_upper_pct'].min():.1f} - {df_clean['bb_upper_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Lower %: {df_clean['bb_lower_pct'].min():.1f} - {df_clean['bb_lower_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Width %: {df_clean['bb_width_pct'].min():.1f} - {df_clean['bb_width_pct'].max():.1f}", file=sys.stderr)
    print(f"  Date range: {df_clean['Date'].min().date()} to {df_clean['Date'].max().date()}", file=sys.stderr)

    # Return DataFrame with Date column and exact feature column order required
    feature_cols = ['Date', 'close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    return df_clean[feature_cols]


# Removed update_history_csv function - no longer needed for traffic history tracking


def load_and_prepare_data(ticker, use_volume=True):
    """Load data exactly matching lstm_finetuning.py approach"""
    try:
        ROOT_DIR = Path(__file__).resolve().parents[1]
        data_path = lambda name: ROOT_DIR / 'data' / f'sp500_{name}_3y.csv'

        # Load all columns exactly like lstm_finetuning.py
        df = pd.DataFrame()
        all_cols = ['open', 'high', 'low', 'close', 'adj_close', 'volume']

        for col_name in all_cols:
            try:
                file_path = data_path(col_name)
                temp_df = pd.read_csv(file_path, usecols=['Date', ticker], parse_dates=['Date'], index_col='Date')
                df[col_name.replace('_', ' ').title().replace(' ', '')] = temp_df[ticker]
            except (FileNotFoundError, KeyError) as e:
                print(f"Failed to load data for ticker '{ticker}' from 'sp500_{col_name}_3y.csv'. Details: {e}", file=sys.stderr)
                sys.exit(1)

        df.rename(columns={'AdjClose': 'Adj Close'}, inplace=True)
        df.dropna(inplace=True)

        # Calculate training end date (5 business days before reference date)
        reference_ts = pd.Timestamp(REFERENCE_DATE)
        train_end_date = reference_ts - BDay(5)  # 5 business days before reference date (day -5)

        # Calculate ideal training start (3.5 years before reference date)
        ideal_start = reference_ts - pd.Timedelta(days=int(3.5 * 365))

        # Use maximum available historical data (start from dataset beginning if needed)
        min_date = df.index.min()
        max_date = df.index.max()
        train_start_date = max(min_date, ideal_start)

        print(f"Dataset range: {min_date.date()} to {max_date.date()}", file=sys.stderr)
        print(f"Training range: {train_start_date.date()} to {train_end_date.date()}", file=sys.stderr)

        # Filter training data using available date range
        train_data = df[(df.index >= train_start_date) & (df.index <= train_end_date)].copy()

        # Check minimum rows requirement
        if len(train_data) < 100:
            print(f"Insufficient training data: {len(train_data)} rows (minimum 100 required)", file=sys.stderr)
            sys.exit(1)

        # Reset index to have Date as column for consistency with other functions
        all_data = df.reset_index()
        train_data = train_data.reset_index()

        return all_data, train_data, train_end_date.date()

    except Exception as e:
        print(f"Error loading data: {e}", file=sys.stderr)
        sys.exit(1)


def create_sequences_with_labels(df, feature_cols=None):
    """
    Create sequences for LSTM training with shift(-5) label creation.

    This function assumes the input df has already been temporally filtered
    to prevent label leakage (i.e., df contains only dates <= target_date - 5).

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with technical indicator features and 'close' column
    feature_cols : list, optional
        List of feature column names. If None, uses all columns except 'Date'

    Returns:
    --------
    tuple
        (X, y) where X has shape (samples, WINDOW_DAYS, num_features) and y is binary labels
    """
    if feature_cols is None:
        feature_cols = ['RSI', 'BB_UPPER_PCT', 'BB_LOWER_PCT', 'BB_WIDTH_PCT', 'volatility_30d', 'log_return', 'Volume']

    # Use existing ret5 column (already created and cleaned in main)
    df = df.copy()
    # 중복 레이블 생성 제거 - ret5는 이미 main에서 생성됨
    # if 'ret5' not in df.columns:
    #     price_col = 'Adj Close' if 'Adj Close' in df.columns else 'close'
    #     df['ret5'] = df[price_col].pct_change(5).shift(-5)

    df['target'] = (df['ret5'] > 0).astype(int)

    # Drop rows where target becomes NaN (last 5 rows)
    df_clean = df.dropna(subset=['target']).copy()

    X, y = [], []

    # Create sequences from the cleaned data
    for i in range(len(df_clean) - WINDOW_DAYS):
        # Extract sequence from df_clean.iloc[i:i+WINDOW_DAYS, feature_cols] with shape (75, 6)
        sequence = df_clean.iloc[i:i+WINDOW_DAYS][feature_cols].values

        # Verify sequence shape
        if sequence.shape != (WINDOW_DAYS, len(feature_cols)):
            print(f"Warning: sequence shape {sequence.shape} != expected ({WINDOW_DAYS}, {len(feature_cols)})", file=sys.stderr)
            continue

        X.append(sequence)

        # Target is at position i + WINDOW_DAYS
        target_idx = i + WINDOW_DAYS
        if target_idx < len(df_clean):
            y.append(df_clean.iloc[target_idx]['target'])

    return np.array(X), np.array(y), len(df_clean)


def focal_loss(gamma=2.0):
    """Focal Loss to address class imbalance more effectively than class weights"""
    def loss(y_true, y_pred):
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)
        p_t = y_true * y_pred + (1 - y_true) * (1 - y_pred)
        return tf.pow(1 - p_t, gamma) * bce
    return loss


def sigmoid(x):
    """Sigmoid function for temperature calibration (from fine-tuning implementation)"""
    return 1 / (1 + np.exp(-x))


def build_lstm_model(input_shape=(WINDOW_DAYS, 7)):
    """
    Build LSTM classifier model matching lstm_finetuning.py exactly

    Architecture: LSTM(64) → Dropout(0.2) → Dense(1, sigmoid)
    Loss: binary_crossentropy with class_weight
    Features: 7 (RSI, BB_UPPER_PCT, BB_LOWER_PCT, BB_WIDTH_PCT, volatility_30d, log_return, Volume)
    """
    model = Sequential([
        Input(shape=input_shape),
        LSTM(64),
        Dropout(0.2),
        Dense(1, activation='sigmoid')
    ])

    model.compile(
        optimizer='adam',
        loss='binary_crossentropy',
        metrics=['accuracy']
    )

    return model


def build_sequence(all_data, indicators_df, scaler, target_date):
    """
    Build a single sequence for prediction on target_date with proper data alignment

    Parameters:
    -----------
    all_data : pandas.DataFrame
        DataFrame with Date and price columns (Date as Timestamp)
    indicators_df : pandas.DataFrame
        DataFrame with technical indicators (must have Date column)
    scaler : sklearn.preprocessing.MinMaxScaler
        Fitted scaler for features
    target_date : date or Timestamp
        Target date for sequence building

    Returns:
    --------
    numpy.ndarray
        Scaled sequence of shape (1, 75, 6)
    """
    # Convert target_date to Timestamp for consistent comparison
    target_ts = pd.Timestamp(target_date)

    # Find target date in indicators data
    date_mask = indicators_df['Date'] == target_ts
    if not date_mask.any():
        # Find nearest available date before or on target
        available_dates = indicators_df['Date'].values
        dates_before = [d for d in available_dates if d <= target_ts]
        if not dates_before:
            raise ValueError(f"No data available before {target_date}")
        actual_date = max(dates_before)
        date_mask = indicators_df['Date'] == actual_date
        print(f"Using nearest date {actual_date.date()} for target {target_date}", file=sys.stderr)

    # Get the row number (not index) in the indicators DataFrame
    matching_rows = indicators_df[date_mask]
    if len(matching_rows) == 0:
        raise ValueError(f"No matching date found for {target_date}")

    # Use iloc position (row number) instead of index
    date_position = matching_rows.index[0]
    row_number = indicators_df.index.get_loc(date_position)

    print(f"Target date: {target_date}, Row position: {row_number}, Total rows: {len(indicators_df)}", file=sys.stderr)

    # Check if we have enough data for WINDOW_DAYS sequence
    if row_number < WINDOW_DAYS:
        raise ValueError(f"Insufficient data before {target_date}: need {WINDOW_DAYS} days, have {row_number}")

    # Extract most recent WINDOW_DAYS (75) days from indicators data using iloc
    feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    # Check if we have the renamed columns
    if 'Adj Close' in indicators_df.columns:
        feature_cols = ['Adj Close', 'Volume', 'RSI', 'BB_UPPER_PCT', 'BB_LOWER_PCT', 'BB_WIDTH_PCT']
    start_row = row_number - WINDOW_DAYS
    end_row = row_number

    print(f"Extracting rows {start_row}:{end_row} for sequence", file=sys.stderr)
    sequence_data = indicators_df.iloc[start_row:end_row][feature_cols].values

    # Verify sequence shape before scaling (7 features)
    expected_features = 7
    if sequence_data.shape != (WINDOW_DAYS, expected_features):
        print(f"Available data shape: {sequence_data.shape}, expected: ({WINDOW_DAYS}, {expected_features})", file=sys.stderr)
        raise ValueError(f"Sequence shape mismatch: expected ({WINDOW_DAYS}, {expected_features}), got {sequence_data.shape}")

    sequence_scaled = scaler.transform(sequence_data)
    return sequence_scaled.reshape(1, WINDOW_DAYS, expected_features)  # (1, 75, 7)


def get_actual_label_if_available(all_data, target_date):
    """
    Get actual label for target_date if data is available

    Returns None for future dates, 0/1 for historical dates
    """
    try:
        # Convert target_date to Timestamp for consistent comparison
        target_ts = pd.Timestamp(target_date)

        date_mask = all_data['Date'] == target_ts
        if not date_mask.any():
            return None

        date_idx = all_data[date_mask].index[0]

        # Check if we have future data for HORIZON_DAYS prediction
        if date_idx + HORIZON_DAYS - 1 >= len(all_data):
            return None

        # Calculate actual label
        price_col = 'Adj Close' if 'Adj Close' in all_data.columns else 'close'
        current_price = all_data.iloc[date_idx-1][price_col]
        future_price = all_data.iloc[date_idx + HORIZON_DAYS - 1][price_col]
        return 1 if future_price > current_price else 0

    except Exception:
        return None


def main():
    """
    Main function with new 6-day prediction approach

    Trains up to day -5, predicts 6 days (-4, -3, -2, -1, 0, +1)
    Uses bias offset only (no calibration/thresholds)
    """
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python lstm_service.py <TICKER> [--no-volume]", file=sys.stderr)
        sys.exit(1)

    ticker = sys.argv[1].upper()
    # Volume is always included in the new data loading approach

    try:
        # Load and prepare data exactly matching lstm_finetuning.py
        all_data, train_data, _ = load_and_prepare_data(ticker)

        # Apply temporal cut-off to prevent label leakage
        # Rule: Training data must end on REFERENCE_DATE - 5 trading days
        target_date = REFERENCE_DATE
        cutoff_offset = 5

        train_data_cutoff, cutoff_date, rows_kept, rows_dropped = apply_temporal_cutoff(
            train_data, target_date, cutoff_offset
        )

        # Logging: Train start/end, rows kept/dropped, target date, cut-off date
        train_start_date = train_data_cutoff['Date'].min().date()
        train_end_date = train_data_cutoff['Date'].max().date()

        print(f"Target date: {target_date}", file=sys.stderr)
        print(f"Cut-off date: {cutoff_date.date()}", file=sys.stderr)
        print(f"Train start: {train_start_date}", file=sys.stderr)
        print(f"Train end: {train_end_date}", file=sys.stderr)
        print(f"Rows kept: {rows_kept}, Rows dropped: {rows_dropped}", file=sys.stderr)

        if len(train_data_cutoff) < 100:
            print(f"Insufficient training data for {ticker} after cutoff", file=sys.stderr)
            sys.exit(1)

        # Compute technical indicators on cutoff training data
        train_indicators = compute_indicators(train_data_cutoff)

        print(f"학습 데이터 형태: {train_indicators.shape}", file=sys.stderr)

        # TODO 1: 컷오프 이후에만 shift(-5) → NaN 5행 반드시 살려라
        # Apply cutoff first, then add horizon labels
        cut_off = pd.Timestamp(REFERENCE_DATE) - BDay(5)  # 2025-05-29
        print(f"Train end      : {cut_off.date()}", file=sys.stderr)

        # Filter training data to cutoff boundary first
        train_indicators_cut = train_indicators[train_indicators['Date'] <= cut_off].copy()
        print(f"Rows kept      : {len(train_indicators_cut)}", file=sys.stderr)

        # Rename columns to match lstm_finetuning.py exactly
        train_indicators_cut = train_indicators_cut.rename(columns={
            'close': 'Adj Close',
            'volume': 'Volume',
            'rsi14': 'RSI',
            'bb_upper_pct': 'BB_UPPER_PCT',
            'bb_lower_pct': 'BB_LOWER_PCT',
            'bb_width_pct': 'BB_WIDTH_PCT'
        })

        # Add log_return and volatility_30d (exactly matching lstm_finetuning.py)
        train_indicators_cut['log_return'] = np.log(train_indicators_cut['Adj Close'] / train_indicators_cut['Adj Close'].shift(1))
        train_indicators_cut['volatility_30d'] = train_indicators_cut['log_return'].rolling(window=30).std() * np.sqrt(252)

        # Apply shift(-5) AFTER cutoff to create horizon labels
        train_indicators_cut['ret5'] = train_indicators_cut['Adj Close'].pct_change(5).shift(-5)
        before_horizon_drop = len(train_indicators_cut)

        # Drop NaN rows from horizon calculation (should be exactly 5)
        train_indicators_cut = train_indicators_cut.dropna(subset=['ret5'])
        horizon_dropped = before_horizon_drop - len(train_indicators_cut)
        print(f"Rows dropped(h): {horizon_dropped}", file=sys.stderr)  # Should be 5

        # Drop other NaN values (technical indicators, etc.)
        before_general_drop = len(train_indicators_cut)
        train_indicators_cut.dropna(inplace=True)
        general_dropped = before_general_drop - len(train_indicators_cut)
        print(f"Rows dropped   : {general_dropped}", file=sys.stderr)

        # Scale the features (exactly matching lstm_finetuning.py)
        feature_cols = ['RSI', 'BB_UPPER_PCT', 'BB_LOWER_PCT', 'BB_WIDTH_PCT', 'volatility_30d', 'log_return', 'Volume']
        scaler = MinMaxScaler(feature_range=(0, 1))
        train_scaled = scaler.fit_transform(train_indicators_cut[feature_cols].values)

        # Create sequences with labels
        train_scaled_df = pd.DataFrame(train_scaled, columns=feature_cols, index=train_indicators_cut.index)
        train_scaled_df['Date'] = train_indicators_cut['Date'].values
        train_scaled_df['Adj Close'] = train_indicators_cut['Adj Close'].values
        train_scaled_df['ret5'] = train_indicators_cut['ret5'].values

        # Update create_sequences_with_labels to use 'Adj Close' instead of 'close'
        X_train, y_train, rows_after_label_creation = create_sequences_with_labels(train_scaled_df, feature_cols)

        # Calculate rows dropped due to horizon (should be exactly 5)
        rows_dropped_horizon = len(train_indicators) - rows_after_label_creation
        print(f"Rows dropped (horizon): {rows_dropped_horizon}", file=sys.stderr)

        print(f"입력 형태: {X_train.shape}", file=sys.stderr)  # Should show (samples, 75, 6)
        print(f"학습 라벨 비율:", round(y_train.mean(), 3), file=sys.stderr)

        # Temperature calibration (exactly matching lstm_finetuning.py)
        epsilon = 1e-6
        p_pos = y_train.mean()
        logit_bias = np.log((p_pos + epsilon) / (1 - p_pos + epsilon))
        temperature = 0.9  # Exact value from lstm_finetuning.py

        print(f"Logit 편향 (b): {logit_bias:.4f}, 온도 (T): {temperature}", file=sys.stderr)

        # Calculate class weights (exactly matching lstm_finetuning.py)
        from sklearn.utils.class_weight import compute_class_weight
        class_weights = compute_class_weight('balanced', classes=np.unique(y_train), y=y_train)
        class_weight_dict = {i: w for i, w in enumerate(class_weights)}
        print(f"클래스 가중치: {{0: '{class_weight_dict.get(0, 1.0):.2f}', 1: '{class_weight_dict.get(1, 1.0):.2f}'}}", file=sys.stderr)

        # Manual validation split (exactly matching lstm_finetuning.py)
        val_size = int(len(X_train) * 0.15)
        if val_size < 1:
            print("Not enough data to create a validation set.", file=sys.stderr)
            sys.exit(1)
        valX, valY = X_train[-val_size:], y_train[-val_size:]
        trainX_no_val, trainY_no_val = X_train[:-val_size], y_train[:-val_size]

        # Build model (exactly matching lstm_finetuning.py with 7 features)
        model = build_lstm_model((WINDOW_DAYS, 7))

        # Early stopping callback (exactly matching lstm_finetuning.py)
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=5,
            restore_best_weights=True
        )

        # Train model (exactly matching lstm_finetuning.py)
        model.fit(
            trainX_no_val, trainY_no_val,
            epochs=30,
            batch_size=32,
            verbose=0,
            validation_data=(valX, valY),
            callbacks=[early_stopping],
            class_weight=class_weight_dict
        )

        # Calculate optimal threshold (exactly matching lstm_finetuning.py)
        val_probs_raw = model.predict(valX, verbose=0).flatten()
        val_logits_raw = np.log((val_probs_raw + epsilon) / (1 - val_probs_raw + epsilon))
        val_logits_adj = (val_logits_raw + logit_bias) / temperature
        val_probs_calibrated = sigmoid(val_logits_adj)

        thresholds = np.arange(0.05, 0.95, 0.01)
        f1_scores = [f1_score(valY, val_probs_calibrated > t, zero_division=0) for t in thresholds]

        if not any(f1_scores) or np.all(np.isnan(f1_scores)):
            optimal_threshold = 0.5
            max_f1 = f1_score(valY, val_probs_calibrated > optimal_threshold, zero_division=0)
        else:
            optimal_idx = np.nanargmax(f1_scores)
            optimal_threshold = thresholds[optimal_idx]
            max_f1 = f1_scores[optimal_idx]

        print(f"최적 임계값 (θ): {optimal_threshold:.2f} (검증 F1: {max_f1:.4f})", file=sys.stderr)

        # Noise factor (exactly matching lstm_finetuning.py)
        noise_factor = 0.01

        # Save model, scaler, bias, and temperature calibration parameters
        ROOT_DIR = Path(__file__).resolve().parents[1]
        model_dir = ROOT_DIR / "data" / "lstm_results"
        model_dir.mkdir(parents=True, exist_ok=True)

        model.save(model_dir / f"{ticker}_model_w75_h5_rsi_bb.keras")
        joblib.dump(scaler, model_dir / f"{ticker}_scaler_w75.pkl")

        # Save temperature calibration parameters to JSON
        calibration_data = {
            "logit_bias": logit_bias,
            "temperature": temperature,
            "epsilon": epsilon
        }
        with open(model_dir / f"{ticker}_calibration.json", 'w') as f:
            json.dump(calibration_data, f)

        # Create full dataset for predictions (exactly matching lstm_finetuning.py)
        # Load all data again for evaluation purposes
        all_data_full, _, _ = load_and_prepare_data(ticker)

        # Compute technical indicators on full dataset
        all_indicators = compute_indicators(all_data_full)

        # Generate 6 specific prediction dates relative to REFERENCE_DATE (June 5, 2025)
        reference_ts = pd.Timestamp(REFERENCE_DATE)
        prediction_dates = [
            reference_ts - BDay(4),  # Day -4
            reference_ts - BDay(3),  # Day -3
            reference_ts - BDay(2),  # Day -2
            reference_ts - BDay(1),  # Day -1
            reference_ts,            # Day 0 (target date)
            reference_ts + BDay(1)   # Day +1 (future prediction)
        ]

        # Filter dates to only those with sufficient historical data in indicators
        dates_to_predict = []
        for d in prediction_dates:
            date_mask = all_indicators['Date'] <= d
            available_rows = date_mask.sum()
            if available_rows >= WINDOW_DAYS:
                dates_to_predict.append(d)
            else:
                print(f"Skipping {d.date()}: only {available_rows} rows available, need {WINDOW_DAYS}", file=sys.stderr)

        if not dates_to_predict:
            print("No dates available for prediction with sufficient historical data", file=sys.stderr)
            sys.exit(1)

        # Log prediction dates with day offsets for verification (using business day count)
        date_info = []
        day_offsets = []
        for d in dates_to_predict:
            day_offset = -np.busday_count(d.date(), pd.Timestamp(REFERENCE_DATE).date())
            date_info.append(d.strftime('%m-%d'))
            day_offsets.append(f"{day_offset:+d}")

        print(f"Predict dates  : {', '.join(date_info)}", file=sys.stderr)
        print(f"day_offset 들  : {', '.join(day_offsets)}", file=sys.stderr)

        # Create evaluation dataset (exactly matching lstm_finetuning.py)
        # Use all_indicators as eval_df equivalent
        eval_df = all_indicators.copy()
        eval_df = eval_df.set_index('Date')

        # Rename columns to match lstm_finetuning.py exactly
        eval_df = eval_df.rename(columns={
            'close': 'Adj Close',
            'volume': 'Volume',
            'rsi14': 'RSI',
            'bb_upper_pct': 'BB_UPPER_PCT',
            'bb_lower_pct': 'BB_LOWER_PCT',
            'bb_width_pct': 'BB_WIDTH_PCT'
        })

        # Add target column for evaluation (matching lstm_finetuning.py)
        eval_df['log_return'] = np.log(eval_df['Adj Close'] / eval_df['Adj Close'].shift(1))
        eval_df['volatility_30d'] = eval_df['log_return'].rolling(window=30).std() * np.sqrt(252)
        eval_df['R_PCT_5D_FWD'] = eval_df['Adj Close'].pct_change(5).shift(-5) * 100
        eval_df.dropna(inplace=True)
        eval_df['target'] = (eval_df['R_PCT_5D_FWD'] > 0).astype(int)

        # Prepare features for scaling (exactly matching lstm_finetuning.py)
        features = ['RSI', 'BB_UPPER_PCT', 'BB_LOWER_PCT', 'BB_WIDTH_PCT', 'volatility_30d', 'log_return', 'Volume']

        # Scale evaluation features (using same feature order as training)
        scaled_eval_features = scaler.transform(eval_df[features])
        eval_data = np.hstack([scaled_eval_features, eval_df[['target']]])

        predictions_log = []
        predicted_directions = []
        actuals = []
        final_probs = []

        # Find target date in eval_df (exactly matching lstm_finetuning.py)
        target_date = REFERENCE_DATE
        target_date_ts = pd.Timestamp(target_date)

        try:
            last_valid_date = eval_df.index.asof(target_date_ts)
            if pd.isna(last_valid_date):
                raise KeyError
            target_date_iloc = eval_df.index.get_loc(last_valid_date)
        except KeyError:
            print(f"Target date {target_date} is outside the evaluation range", file=sys.stderr)
            sys.exit(1)

        # Note: We use dates_to_predict list instead of iloc ranges for better date alignment

        # Use the dates_to_predict list to ensure correct date alignment
        look_back = WINDOW_DAYS
        num_features = len(features)

        # Map dates_to_predict to eval_df indices for proper sequence extraction
        date_to_iloc_map = {}
        for date in dates_to_predict:
            try:
                # Find the closest available date in eval_df
                closest_date = eval_df.index.asof(date)
                if pd.notna(closest_date):
                    iloc_pos = eval_df.index.get_loc(closest_date)
                    date_to_iloc_map[date] = iloc_pos
            except (KeyError, IndexError):
                continue

        for pred_date in dates_to_predict:
            if pred_date not in date_to_iloc_map:
                continue

            i = date_to_iloc_map[pred_date]
            sequence_end_index = i
            sequence_start_index = sequence_end_index - look_back + 1

            if sequence_start_index < 0:
                continue

            # Handle day +1 case (future prediction beyond available data)
            if pred_date > eval_df.index.max():
                # This is day +1 - use the last available sequence
                sequence_data = eval_data[sequence_start_index - 1 : sequence_end_index]
                current_date = pred_date
            else:
                sequence_data = eval_data[sequence_start_index : sequence_end_index + 1]
                current_date = pred_date

            if sequence_data.shape[0] != look_back:
                continue

            # Extract features and predict (exactly matching lstm_finetuning.py)
            input_sequence = sequence_data[:, :num_features]
            predict_day_features = np.reshape(input_sequence, (1, look_back, num_features))

            raw_prob = model.predict(predict_day_features, verbose=0)[0][0]
            prob_clipped = np.clip(raw_prob, epsilon, 1 - epsilon)
            logit = np.log(prob_clipped / (1 - prob_clipped))
            calibrated_prob = 1 / (1 + np.exp(-(logit + logit_bias) / temperature))
            prob_up = calibrated_prob

            # Add random walk noise (exactly matching lstm_finetuning.py)
            # Calculate prediction horizon based on position in dates_to_predict list
            prediction_horizon_day = dates_to_predict.index(pred_date)
            noise_std_dev = prediction_horizon_day * noise_factor
            noise = np.random.normal(0, noise_std_dev)
            prob_up += noise
            prob_up = np.clip(prob_up, 0, 1)

            final_probs.append(prob_up)

            predicted_direction = 1 if prob_up > optimal_threshold else 0

            # Calculate day offset using business day count (영업일 차이로 교정)
            day_offset = -np.busday_count(pred_date.date(), pd.Timestamp(REFERENCE_DATE).date())

            # Check if actual direction is available
            actual_direction = None
            try:
                if pred_date <= eval_df.index.max() and pred_date in eval_df.index and 'target' in eval_df.columns:
                    actual_direction = int(eval_df.loc[pred_date, 'target'])
            except (KeyError, IndexError):
                actual_direction = None

            # Only include in accuracy calculation if actual direction is available
            if actual_direction is not None:
                predicted_directions.append(predicted_direction)
                actuals.append(actual_direction)

            predictions_log.append({
                "date": current_date.strftime('%Y-%m-%d'),
                "day_offset": day_offset,
                "prob_up": float(f"{prob_up:.4f}"),
                "predicted_direction": predicted_direction,
                "actual_direction": actual_direction,
                "prediction_horizon": 5
            })

        if not predictions_log:
            print("Could not generate any predictions for the target date range.", file=sys.stderr)
            sys.exit(1)

        # TODO 3: 성능 집계 루프에서 히스토리컬 5개만 메트릭 계산
        # Filter to only historical predictions with actual labels (day_offset <= 0 and actual_direction is not None)
        hist_preds = [p for p in predictions_log if p["day_offset"] <= 0 and p["actual_direction"] is not None]

        # Assert exactly 5 historical predictions with labels
        assert len(hist_preds) == 5, f"⚠️ 히스토리컬 라벨 5개가 필요합니다! 현재: {len(hist_preds)}개"

        # Calculate accuracy using only historical predictions
        hist_predicted_directions = [p["predicted_direction"] for p in hist_preds]
        hist_actuals = [p["actual_direction"] for p in hist_preds]

        hits_last5 = sum(p == a for p, a in zip(hist_predicted_directions, hist_actuals))
        print(f"Accuracy       : {hits_last5}/5 (calculated on 5 historical predictions)", file=sys.stderr)

        # TODO 4: day +1 확률로 신호등 결정
        day_plus_1_prediction = None
        day_plus_1_prob_up = 0.0

        # Find day +1 prediction (2025-06-06)
        for pred in predictions_log:
            if pred.get("day_offset") == 1:  # Day +1
                day_plus_1_prediction = pred
                day_plus_1_prob_up = pred['prob_up']
                print(f"Traffic-light 로그     : based on {pred['date']} probability", file=sys.stderr)
                break

        # Check if LSTM should be deactivated based on accuracy (0 or 1 correct predictions)
        is_deactivated = hits_last5 <= 1

        if day_plus_1_prediction is None:
            print(f"[LSTM accuracy: {hits_last5}, No Day +1 prediction available, Traffic light: RED]", file=sys.stderr)
            traffic_light = "RED"
            traffic_light_status = "red"
        elif is_deactivated:
            # Deactivate LSTM when accuracy is too low (0 or 1 correct predictions)
            traffic_light = "INACTIVE"
            traffic_light_status = "inactive"
            print(f"[LSTM accuracy: {hits_last5}, Prediction probability up: {day_plus_1_prob_up:.3f}, Traffic light: INACTIVE (비활성화)]", file=sys.stderr)
        else:
            # Normal traffic light logic based on day +1 prediction probability
            if day_plus_1_prob_up > 0.525:
                traffic_light = "GREEN"
                traffic_light_status = "green"
            elif day_plus_1_prob_up >= 0.475:
                traffic_light = "YELLOW"
                traffic_light_status = "yellow"
            else:
                traffic_light = "RED"
                traffic_light_status = "red"

            print(f"[LSTM accuracy: {hits_last5}, Prediction probability up: {day_plus_1_prob_up:.3f}, Traffic light: {traffic_light}]", file=sys.stderr)

        # Backend output for debugging
        print(f"백엔드 출력 - 심볼: {ticker}, 정확도: {hits_last5}, 예측확률: {day_plus_1_prob_up:.4f}, 신호등: {traffic_light}", file=sys.stderr)

        # Create result with SpeedTraffic compatible structure
        result = {
            "symbol": ticker,
            "predictions": predictions_log,
            "traffic_light": traffic_light_status,
            "accuracy": hits_last5,
            "prediction_probability_up": day_plus_1_prob_up,
            "is_deactivated": is_deactivated,
            "status": "비활성화" if is_deactivated else "활성화",
            "color": "grey" if is_deactivated else traffic_light_status,
            "message": f"정확도 {hits_last5}/5 - {'비활성화' if is_deactivated else traffic_light}"
        }

        # Output result as JSON
        print(json.dumps(result, ensure_ascii=False))

    except Exception as e:
        print(f"Error in main execution: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()